/*
 * Test Type Table
 * Read-only table for displaying test type information
 * Uses ReadOnlyTable for standardized WET-BOEW DataTables implementation
 * Follows GC Web Standards and eliminates code duplication
 */
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { formatStatusDisplay } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    test: TEST_CONFIGS
};

class TestTypeTable extends ReadOnlyTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || TEST_CONFIGS.ui.testTypeTable.containerId,
            tableId: options.tableId || TEST_CONFIGS.ui.testTypeTable.tableId,
            entityConfig: {
                ...TEST_CONFIGS.ui.testTypeTable,
                entityTerms: TEST_CONFIGS.ui.testTypeTable.entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });

        // Store user info for conditional columns
        this.userInfo = options.userInfo || null;

        this.entityFormatters = {
            testStatus: (value) => {
                const statusLabels = GLOBAL_CONFIGS.ui.status;
                return formatStatusDisplay(value, statusLabels);
            }
        };
    }

    // Fetch test type data from API
    async fetchData() {
        try {
            const testTypes = await fetchTestTypesForLab(this.userInfo);
            return testTypes || [];
        } catch (error) {
            console.error('Failed to fetch test types:', error);
            throw error;
        }
    }

    // Override create method to pass user info for conditional columns
    async create(config = {}) {
        // Update userInfo if provided in config
        if (config.userInfo) {
            this.userInfo = config.userInfo;
        }

        // Set current config for conditional column evaluation
        this.currentConfig = {
            role: this.userInfo?.role || 'user',
            ...config
        };

        return super.create(config);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create test type table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }

    // Post-initialization setup (optional override)
    postInitialize() {
        // No additional setup needed for read-only test type table
    }
}

// Factory function to create test type table instances
export function createTestTypeTable(options = {}) {
    return new TestTypeTable(options);
}

// Create and export singleton instance for default usage
export const testTypeTable = new TestTypeTable();
