# Sed-LIMS Frontend Development Guide

> **Purpose**: AI Assistant reference for Sed-LIMS frontend development

## 🎯 ESSENTIAL CODING STANDARDS

### Core Rules
- **jQuery DOM operations**: Use jQuery for all DOM manipulation (GCWeb compatibility)
- **ES6 modules + jQuery**: Modern imports with jQuery for DOM operations
- **GC Standards**: Use only official gcweb/wet-boew components
- **Config-driven**: Use entity-specific configs for all implementations
- **English comments**: All code comments must be in English
- **Bilingual support**: EN/FR support mandatory

### Import Order Standard
1. Base classes (table-base.js)
2. Helper functions (message-helpers.js, format-helpers.js)
3. API services (*-api.js)
4. Configuration files (*-configs.js)

### Project Structure
```
frontend/
├── public/assets/js/
│   ├── core/                    # Shared functionality
│   │   ├── auth/               # Authentication & authorization
│   │   ├── services/           # API service layer
│   │   ├── config/             # Configuration files
│   │   ├── i18n/               # Internationalization
│   │   └── helpers/            # Utility functions
│   └── pages/                   # Page-specific code
│       ├── auth/               # Authentication pages
│       ├── home/               # Home page
│       ├── account/            # Account management
│       ├── lab/                # Laboratory management
│       └── requisition/        # Requisition management
└── server.js                  # Development server
```

### Development Setup
```bash
# Prerequisites: Node.js 18+, Backend API on localhost:8000
cd frontend/
npm install
npm run dev        # Start development server (localhost:3000)

# Environment Variables (injected by server.js)
window.ENV_BACKEND_API_URL  # Backend API URL
```

## 🔧 CORE PATTERNS

### JavaScript Patterns
```javascript
// DOM Ready Pattern
$(document).ready(function() {
    initializePage();
});

// DOM Operations (jQuery required for GCWeb compatibility)
$('#element').addClass('active');
$(document).on('click', '.button', handler);

// Error Handling (async/await + try/catch)
async function initializePage() {
    try {
        const data = await ApiService.getData();
        // Handle success
    } catch (error) {
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// AVOID: Vanilla JS DOM methods (breaks GCWeb compatibility)
// ❌ document.getElementById()
// ❌ element.addEventListener()
```

### HTML Template Requirements
For authenticated pages, include the authentication middleware script:
```html
<!-- Authentication Middleware (required for authenticated pages) -->
<script type="module" src="/assets/js/core/auth/authentication.js"></script>
<!-- Page-specific JavaScript -->
<script type="module" src="/assets/js/pages/your-page/your-page.js"></script>
```

### Standard Page Template
```javascript
// Standard imports for authenticated pages
import { showMessage } from '../../core/helpers/message-helpers.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';
import { ENTITY_CONFIGS } from '../../core/config/entity-configs.js'; // If needed

// Message config maps (required for showMessage)
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    entity: ENTITY_CONFIGS  // Add entity configs as needed
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN, // Optional: specific role required
    onAuthSuccess: initializePageName,                         // Function to call on successful auth
    onAuthFailure: showAccessDeniedError                       // Optional: custom error handler
};

// Page initialization (called by authentication middleware after successful auth)
async function initializePageName(userInfo) {
    try {
        // Page-specific logic here
        await loadPageData(userInfo);
        setupEventHandlers();
    } catch (error) {
        console.error('Page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

async function loadPageData(userInfo) {
    // API calls and data loading
}

function setupEventHandlers() {
    // Event listener setup
}

// Custom access denied handler (optional)
function showAccessDeniedError() {
    showMessage('#wb-cont', 'auth.messages.errors', 'accessDenied', MESSAGE_CONFIG_MAPS);

    // Redirect to home page after showing error
    // Access denied: 3s delay (user operation feedback - allows reading error message)
    setTimeout(() => {
        redirectToPageByKey('home');
    }, GLOBAL_CONFIGS.navigation.redirectDelay);
}
```

### Authentication Configuration
- `requiredRole` (optional): Specific role required to access the page
- `onAuthSuccess` (required): Function to call after successful authentication
- `onAuthFailure` (optional): Custom error handler for authentication failures

### Available Roles
- `GLOBAL_CONFIGS.application.roles.SCIENTIST`
- `GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL`
- `GLOBAL_CONFIGS.application.roles.LAB_ADMIN`
```
## 💬 MESSAGING SYSTEM

### Entity vs Field Concepts

**Important**: Understand the distinction between Entity and Field concepts:

- **Entity**: Business entities (requisition, sample, test type) - database entities with CRUD operations
- **Field**: Form input fields (Test name, Test description) - user input validation

**Placeholder Usage**:
- Entity operations use `{entity}` placeholder with entityTerms
- Field validation uses `{field}` placeholder with fieldTerms

### Core Message Patterns
```javascript
// Basic message display
showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);

// Template messages with placeholders
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#page-success-container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm  // "test types created successfully!"
});

// Field validation messages
const fieldTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.fieldTerms.name }, 'message');
showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm  // "Test name is required."
});

// Length validation
showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm,
    maxLength: GLOBAL_CONFIGS.form.standardLengths.shortName
});
```

### Message Rules
- **NEVER use `alert()`** - Use GC-compliant displays only
- **Page level only** - All messages below page heading
- **Auto-scroll** - User scrolled to top automatically
- **Bilingual** - All messages support EN/FR
- **Types**: success (auto-dismiss), danger (persistent), warning (auto-dismiss), info (auto-dismiss)

### Message Types & Behavior

#### 🚨 DANGER (Persistent - User must dismiss)
**Critical errors requiring immediate attention:**
- Server/API failures
- Authentication/authorization failures
- Required field validation errors
- System errors blocking progress

```javascript
showMessage('#container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
showMessage('#container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
```

#### ⚠️ WARNING (Auto-dismiss 3s)
**Important but non-blocking issues:**
- Duplicate name conflicts
- Session expiration warnings
- Non-critical validation issues

```javascript
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});
```

#### ✅ SUCCESS (Auto-dismiss 3s)
**Successful operations:**
- CRUD operations completed
- Form submissions successful
- Bulk operations finished

```javascript
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});
```

#### ℹ️ INFO (Auto-dismiss 3s)
**General notifications:**
- User-initiated cancellations
- Status updates
- General feedback

```javascript
showMessage('#container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);
```
## 🔧 CONFIGURATION SYSTEM

### Configuration Files
- **global-configs.js**: App-wide settings (roles, API, common UI, message templates)
- **auth-configs.js**: Authentication (login errors, AAD messages, access control)
- **test-configs.js**: Test management configurations
- **requisition-configs.js**: Requisition management configurations

### Standard Config Structure
All configs follow this three-section pattern:

```javascript
export const ENTITY_CONFIGS = {
    // ===== FUNCTIONAL SETTINGS =====
    form: { maxLength: 50 },
    api: { timeout: 30000 },
    navigation: { redirectDelay: 3000 },

    // ===== UI TEXT (Bilingual) =====
    ui: {
        table: {
            entityTerms: { en: 'items', fr: 'éléments' }
        },
        fieldTerms: {
            name: { en: 'Name', fr: 'Nom' },
            description: { en: 'Description', fr: 'Description' }
        }
    },

    // ===== MESSAGES (Bilingual) =====
    messages: {
        // Most messages now use global templates
        // Add entity-specific messages here only when needed
    }
};
```

### Key Global Configurations
```javascript
// Application roles
GLOBAL_CONFIGS.application.roles.LAB_ADMIN
GLOBAL_CONFIGS.application.roles.SCIENTIST
GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL

// API configuration
GLOBAL_CONFIGS.api.baseUrl

// Form validation standards
GLOBAL_CONFIGS.form.standardLengths.shortName      // 50
GLOBAL_CONFIGS.form.standardLengths.description    // 200

// Message templates (use with entity placeholders)
GLOBAL_CONFIGS.messages.success.templates.entityCreated
GLOBAL_CONFIGS.messages.errors.templates.fieldRequired
GLOBAL_CONFIGS.messages.errors.templates.fieldTooLong
GLOBAL_CONFIGS.messages.warnings.templates.duplicateName

// Table templates (use with entity terms)
GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable
GLOBAL_CONFIGS.ui.table.messageTemplates.search
```

### Message Path Examples
```javascript
// Authentication messages
showMessage('#container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);

// Global server errors
showMessage('#container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);

// Template messages with placeholders
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
const fieldTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.fieldTerms.name }, 'message');

// Entity operations
showMessage('#container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm  // "test types created successfully!"
});

// Field validation
showMessage('#container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm  // "Test name is required."
});

// Length validation
showMessage('#container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm,
    maxLength: GLOBAL_CONFIGS.form.standardLengths.shortName
});

// Duplicate warnings
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});

// Common info messages
showMessage('#container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);
```

### New Global Configuration Usage
```javascript
// Application roles
if (userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN) { ... }

// API configuration
fetch(`${GLOBAL_CONFIGS.api.baseUrl}/endpoint`)

// Common UI text
const saveText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
const roleText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.roles[roleName] }, 'message');

// Form validation with dynamic lengths
if (name.length > GLOBAL_CONFIGS.form.standardLengths.shortName) { ... }
```

### Config Best Practices
- **Verify before deletion**: Search codebase for references
- **Use correct paths**: `auth.messages.errors`, `global.messages.errors.server`
- **Update consistently**: When restructuring, update ALL references
- **Prefer templates**: Use global templates over entity-specific messages

### Text Localization
```javascript
// ✅ CORRECT - Always use localized text
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
const text = getLocalizedText(GLOBAL_CONFIGS.ui.common.loading, 'message');

// ❌ WRONG - Never hardcode text
const text = 'Loading...';
```

## 🎨 GC WEB STANDARDS

### Form Components
```html
<!-- Checkboxes/Radio (ALWAYS use gc-chckbxrdio) -->
<div class="checkbox gc-chckbxrdio">
    <input type="checkbox" id="test-active">
    <label for="test-active">Active</label>
</div>

<!-- Field labels -->
<span class="field-name">Test Name</span>
<strong class="required">(required)</strong>
```

### Button Patterns
```javascript
// Simple disable pattern (avoid complex loading states)
button.disabled = true;
try {
    await performAction();
    // Stay disabled on success
} catch (error) {
    button.disabled = false; // Re-enable on error
}
```

## 🔄 NAVIGATION PATTERNS

### Redirect Delays
```javascript
// Immediate (0ms) - Authentication/security
redirectToPageByKey('home', 0);        // Login success
redirectToPageByKey('login', 0);       // Auth failure

// Default (3s) - User operations (allows message reading)
redirectToPageByKey('manageTestTypes'); // Uses default 3s delay
redirectToPageByKey('home');           // Uses default 3s delay

// Custom delays (rare, must be justified)
redirectToPageByKey('home', 5000);     // Custom with reason
```

### Rules
- **Immediate (0ms)**: Authentication, access control, security
- **Default (3s)**: User operations with feedback messages
- **Custom**: Special cases only, document reasoning

## 📚 ESSENTIAL IMPORTS

```javascript
// Core functionality
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';

// Core configs (always needed)
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Entity configs (as needed)
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';

// API services
import { TestApi } from '../../core/services/test-api.js';
import { UserApi } from '../../core/services/user-api.js';
import { RequisitionApi } from '../../core/services/requisition-api.js';

// Message config maps (required for showMessage)
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS,           // Add as needed
    requisition: REQUISITION_CONFIGS  // Add as needed
};
```

## 🔍 TROUBLESHOOTING

### Common Issues
```javascript
// Issue: "Message configuration not found"
// ❌ Wrong path
showMessage('#container', 'form', 'duplicateName', MESSAGE_CONFIG_MAPS);
// ✅ Correct path
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS);

// Issue: Config not found in MESSAGE_CONFIG_MAPS
// Solution: Ensure config is imported and added to maps
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS  // ← Make sure this is imported
};
```

## 📝 CREATING NEW CONFIGURATION FILES

### Configuration File Template
When creating new entity configurations, copy this template and modify as needed:

```javascript
/*
 * [Entity Name] Configuration
 * See Development_Guide.md for structure documentation and examples
 */

export const ENTITY_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Technical configurations (not user-facing)

    form: {
        // Example: maxNameLength: 50, requiredFields: ['name', 'email']
    },

    table: {
        // Example: defaultPageSize: 10, sortColumn: 'name'
    },

    navigation: {
        // Example: redirectDelay: 3000, defaultRoute: '/dashboard'
    },

    api: {
        // Example: entitySpecificEndpoint: '/custom-endpoint'
        // Note: Common API settings (baseUrl, timeout) are in GLOBAL_CONFIGS.api
    },

    // ===== 2. UI TEXT (Bilingual) =====
    // User-facing text organized by component
    ui: {
        table: {
            title: { en: 'Entity Management', fr: 'Gestion des entités' },
            containerId: 'entity-table-container',
            tableId: 'entity-table',

            // Column headers for i18n
            columns: {
                name: { en: 'Name', fr: 'Nom' },
                status: { en: 'Status', fr: 'Statut' }
                // Common columns (dateModified, active) use GLOBAL_CONFIGS.ui.table.columns
            },

            // Actions configuration
            actions: {
                add: { en: 'Add New', fr: 'Ajouter nouveau' }
                // Common actions use GLOBAL_CONFIGS.ui.buttons
            },

            // Entity terms for table messages
            entityTerms: {
                en: 'items',
                fr: 'éléments'
            },

            tableSchema: {
                columns: [
                    {
                        key: 'name',
                        header: { en: 'Name', fr: 'Nom' },
                        accessor: (row) => row.name,
                        formatter: 'safeText',
                        sort: 'text'
                    },
                    {
                        key: 'dateModified',
                        header: { en: 'Date Modified', fr: 'Date de modification' },
                        accessor: (row) => row.updated_at || row.created_at,
                        formatter: 'formatDate',
                        sort: 'date'
                    },
                    {
                        key: 'status',
                        header: { en: 'Status', fr: 'Statut' },
                        accessor: (row) => row.is_active,
                        formatter: 'entityStatus',
                        sort: false
                    }
                ],
                defaultOptions: {
                    order: [[1, "desc"]]
                }
            },
            managementTableSchema: {
                checkboxColumn: {
                    key: 'actions',
                    header: { en: 'Status', fr: 'Statut' },
                    accessor: (row) => row,
                    formatter: 'entityCheckbox',
                    sort: false,
                    orderable: false
                },
                defaultOptions: {
                    columnDefs: [{ orderable: false, targets: [-1] }]
                }
            }
        },

        // Field terms for global template substitution
        fieldTerms: {
            name: {
                en: 'Name',
                fr: 'Nom'
            },
            description: {
                en: 'Description',
                fr: 'Description'
            }
        }
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Entity-specific messages that cannot use global templates
    // Most messages now use global templates:
    // - Required fields: global.messages.errors.templates.fieldRequired (uses {field} placeholder)
    // - Length validation: global.messages.errors.templates.fieldTooLong (uses {field} placeholder)
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Duplicate names: global.messages.warnings.templates.duplicateName (uses {entity} placeholder)
    // - Cancelled operations: global.messages.info.common.cancelled
    messages: {
        // Add entity-specific message sections here as needed:
        // errors: { ... },
        // success: { ... },
        // warnings: { ... },
        // info: { ... }
    }
};
```

### Step-by-Step Creation Guide

1. **Copy the template above**
2. **Rename the export**: `ENTITY_CONFIGS` → `YOUR_ENTITY_CONFIGS`
3. **Update functional settings**: Add entity-specific technical configurations
4. **Define UI text**:
   - Update table title and columns
   - Define entityTerms for table messages
   - Define fieldTerms for validation messages
5. **Add entity-specific messages**: Only if global templates are insufficient
6. **Import in your pages**: Add to MESSAGE_CONFIG_MAPS

### Configuration Best Practices

- **Follow the three-section structure**: Functional Settings → UI Text → Messages
- **Use Entity vs Field concepts correctly**:
  - entityTerms for business entities (e.g., "test types")
  - fieldTerms for form fields (e.g., "Test name")
- **Prefer global templates**: Use global message templates when possible
- **Bilingual support**: Always provide both EN and FR text
- **Consistent naming**: Use clear, descriptive configuration keys

## 📊 TABLE IMPLEMENTATION STANDARDS

### Table Architecture Pattern
All tables in Sed-LIMS follow a standardized object-oriented architecture using the `BaseTable` class hierarchy. This ensures consistency, maintainability, and full utilization of shared functionality.

#### Standardized Architecture Approach

**Schema-Driven Object-Oriented Architecture (Required)**:
- All tables must extend from `BaseTable` hierarchy
- Tables use `tableSchema` configuration for automatic rendering
- Provides inheritance, lifecycle management, and standardized error handling
- Ensures consistent WET-BOEW integration and GC standards compliance
- Eliminates code duplication through shared functionality and schema-driven rendering

#### Base Table Classes

**Available Base Classes**:
- `ReadOnlyTable` - For display-only tables (extends BaseTable)
- `BulkCheckboxTable` - For tables with bulk checkbox operations (extends BaseTable)

**Schema-Driven Approach**: Tables now use `tableSchema` configuration for automatic rendering. Manual `getTableHeaders()` and `buildTableRows()` methods are only required for legacy tables or special cases not covered by schema.

#### Table Schema Configuration Standard

**Schema-Driven Tables (Required for New Tables)**:
All new tables must use the `tableSchema` configuration approach for consistency and maintainability.

**Schema Structure**:
```javascript
tableSchema: {
    columns: [
        {
            key: 'fieldName',                           // Data field identifier
            header: { en: 'Header Text', fr: 'Texte d\'en-tête' }, // Direct header definition
            accessor: (row) => row.field_name,          // Data extraction function
            formatter: 'formatterName',                 // Formatter reference (optional)
            cell: (value, row) => customHtml,           // Custom cell function (optional)
            sort: 'text'|'date'|'number'|false,         // Sort type
            conditional: true,                          // Column is conditionally shown (optional)
            condition: (config) => config.role === 'admin', // Condition function (optional)
            orderable: false,                           // Disable sorting (optional)
            visible: false,                             // Hide column (optional)
            width: '100px'                              // Column width (optional)
        }
    ],
    defaultOptions: {
        order: [[1, "desc"]],                           // Default DataTables options
        columnDefs: [{ orderable: false, targets: [2] }]
    }
}
```

**Available Formatters**:
- `safeText` - HTML escape with "Not Available" fallback
- `escapeHtml` - Basic HTML escaping
- `formatDate` - Date formatting with fallback
- `formatDateWithFallback` - Date with updated_at/created_at fallback
- `notAvailable` - "Not Available" text
- `roleDisplay` - Role display names
- `testStatus` - Test status display (entity-specific)
- `testStatusCheckbox` - Test status checkbox (entity-specific)

**Entity-Specific Formatters**:
Add custom formatters in table constructor:
```javascript
this.entityFormatters = {
    customFormatter: (value, row) => {
        // Custom formatting logic
        return formattedValue;
    }
};
```

#### Standard Table Implementation Template

**Read-Only Table Template**:

```javascript
/*
 * [Entity] Table
 * Read-only table for displaying [entity] information
 * Uses ReadOnlyTable for standardized WET-BOEW DataTables implementation
 * Follows GC Web Standards and eliminates code duplication
 */
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { escapeHtml, formatDate } from '../../core/helpers/format-helpers.js';
import { [EntityApi] } from '../../core/services/[entity]-api.js';
import { [ENTITY]_CONFIGS } from '../../core/config/[entity]-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    [entity]: [ENTITY]_CONFIGS
};

class [Entity]Table extends ReadOnlyTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || [ENTITY]_CONFIGS.ui.[entityTable].containerId,
            tableId: options.tableId || [ENTITY]_CONFIGS.ui.[entityTable].tableId,
            entityConfig: {
                ...[ENTITY]_CONFIGS.ui.[entityTable],
                entityTerms: [ENTITY]_CONFIGS.ui.[entityTable].entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });

        // Add entity-specific formatters if needed
        this.entityFormatters = {
            entityStatus: (value, row) => {
                // Custom status formatter for this entity
                return value ? 'Active' : 'Inactive';
            }
        };
    }

    // Fetch data from API with error handling
    async fetchData() {
        try {
            const data = await new Promise((resolve, reject) => {
                [EntityApi].get[Entity]Data()
                    .done(resolve)
                    .fail(reject);
            });
            return data || [];
        } catch (error) {
            console.error('Failed to fetch [entity] data:', error);
            throw error;
        }
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create [entity] table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Factory function to create [entity] table instances
export function create[Entity]Table(options = {}) {
    return new [Entity]Table(options);
}

// Create and export singleton instance for default usage
export const [entity]Table = new [Entity]Table();
```

**Management Table Template**:

```javascript
/*
 * [Entity] Manager
 * Specialized table for managing [entity] with bulk operations
 * Uses BulkCheckboxTable for standardized WET-BOEW DataTables implementation
 * Follows GC Web Standards and eliminates code duplication
 */
import { BulkCheckboxTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { escapeHtml, formatDate } from '../../core/helpers/format-helpers.js';
import { [EntityApi] } from '../../core/services/[entity]-api.js';
import { [ENTITY]_CONFIGS } from '../../core/config/[entity]-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    [entity]: [ENTITY]_CONFIGS
};

class [Entity]Manager extends BulkCheckboxTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || [ENTITY]_CONFIGS.ui.[entityTable].containerId,
            tableId: options.tableId || [ENTITY]_CONFIGS.ui.[entityTable].tableId,
            entityConfig: {
                ...[ENTITY]_CONFIGS.ui.[entityTable],
                ...[ENTITY]_CONFIGS.ui.[entityTable].manage,
                entityTerms: [ENTITY]_CONFIGS.ui.[entityTable].entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });
        this.userInfo = options.userInfo || null;
    }

    // Fetch data from API with error handling
    async fetchData() {
        try {
            return await [fetchHelperFunction](this.userInfo);
        } catch (error) {
            console.error('Failed to fetch [entity] data:', error);
            throw error;
        }
    }

    // All table functionality is now handled by tableSchema
    // No need to implement getTableHeaders() or buildTableRows()

    // Build checkbox column for management
    buildCheckboxColumn(item) {
        const itemId = item.[entity]_id;
        const itemName = escapeHtml(item.name || '');
        const isActive = item.is_active;
        const checkedAttr = isActive ? 'checked' : '';

        return `<div class="checkbox gc-chckbxrdio">
            <input type="checkbox"
                   id="[entity]-active-${itemId}"
                   class="[entity]-status-checkbox"
                   data-[entity]-id="${itemId}"
                   data-original-state="${isActive}"
                   ${checkedAttr}>
            <label for="[entity]-active-${itemId}">
                <span class="wb-inv">Toggle active status for ${itemName}</span>
            </label>
        </div>`;
    }

    // Initialize event handlers for management functionality
    initializeEventHandlers() {
        const self = this;

        // Handle checkbox changes
        $(document).off('change', '.[entity]-status-checkbox').on('change', '.[entity]-status-checkbox', function() {
            self.handleCheckboxChange($(this));
        });

        // Handle save and cancel actions
        $(document).off('click', '#save-changes-btn').on('click', '#save-changes-btn', function() {
            self.handleSaveChanges();
        });

        $(document).off('click', '#cancel-changes-btn').on('click', '#cancel-changes-btn', function() {
            self.handleCancelChanges();
        });

        this.trackChanges(false);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create [entity] manager "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Export class and factory function
export { [Entity]Manager };
export const [entity]Manager = new [Entity]Manager();
```

#### When to Use Each Base Class

**Use ReadOnlyTable When**:
- Display-only tables without editing functionality
- Simple data presentation requirements
- No state management or user interactions needed
- Examples: Staff table, Requisition table

**Use BulkCheckboxTable When**:
- Tables require bulk operations (enable/disable, delete, etc.)
- State management for tracking changes
- Checkbox-based user interactions
- Save/cancel functionality needed
- Examples: Test Type manager

#### Usage Examples

**Read-Only Table Usage**:
```javascript
// Standard usage with default configuration
await staffTable.create();

// Custom configuration
await staffTable.create({
    containerId: 'custom-container-id',
    tableId: 'custom-table-id'
});
```

**Management Table Usage**:
```javascript
// Standard usage
await testTypeManager.create({
    userInfo: userInfo
});

// Custom configuration
await testTypeManager.create({
    containerId: 'custom-container-id',
    tableId: 'custom-table-id',
    userInfo: userInfo
});
```

#### Required Components and Helpers

**Base Table Classes** (`table-base.js`):
- `BaseTable` - Abstract base class with common functionality
- `ReadOnlyTable` - For display-only tables (extends BaseTable)
- `BulkCheckboxTable` - For bulk operations (extends BaseTable)
- Provides standardized loading, error handling, and WET-BOEW integration

**Table Helper Functions** (`table-helpers.js`):
- `initializeWETTable(tableId)` - WET-BOEW DataTables initialization
- `buildTableMessageConfig(entityTerms)` - Localized message configuration
- `buildTableHtml(options)` - Complete table HTML structure (used by BaseTable)

**Format Helpers** (`format-helpers.js`):
- `escapeHtml(text)` - HTML escaping for security (required for all user data)
- `formatDate(dateString, format)` - Date formatting with localization
- `getRoleDisplayName(roleName)` - Role display names
- `formatStatusDisplay(isActive, statusLabels)` - Status display formatting
- `formatNotAvailable()` - "Not available" text formatting

**Message Helpers** (`message-helpers.js`):
- `showMessage(containerId, configPath, messageKey, configMaps, placeholders)` - Standardized message display
- Automatic scrolling and message management
- Required for all error handling

**Configuration Requirements**:
- All tables must define `MESSAGE_CONFIG_MAPS` with required config mappings
- Use entity-specific configs for table structure and messages (e.g., STAFF_CONFIGS, TEST_CONFIGS, REQUISITION_CONFIGS)
- Leverage `GLOBAL_CONFIGS` for common UI elements only
- Each entity should have its own configuration file following the pattern: `[entity]-configs.js`
- Configuration files must include: containerId, tableId, entityTerms, and tableSchema

#### Current Table Implementations

**Read-Only Display Tables**:
- `StaffTable` - Laboratory staff information display (uses STAFF_CONFIGS)
- `RequisitionTable` - Requisition display with queue management (uses REQUISITION_CONFIGS)
- `TestTypeTable` - Test types in read-only mode (uses TEST_CONFIGS)

**Management Tables**:
- `TestTypeTableManager` - Test type management with bulk operations (uses TEST_CONFIGS)

**Implementation Requirements**:
- All tables must extend appropriate base class (`ReadOnlyTable` or `BulkCheckboxTable`)
- All tables must define `tableSchema` in entity configuration
- All tables must implement required method: `fetchData()`
- All tables must include `handleError()` method for standardized error handling
- Management tables must implement event handlers and change tracking
- Legacy methods (`getTableHeaders()`, `buildTableRows()`) only needed for special cases

#### Naming Conventions

**Updated Naming Standards (2025)**:
- **Read-only tables**: Always end with `-table.js` (e.g., `staff-table.js`, `test-type-table.js`)
- **Batch operation tables**: Always end with `-table-manager.js` (e.g., `test-type-table-manager.js`)
- **Class names**: Follow PascalCase with descriptive suffixes (`StaffTable`, `TestTypeTableManager`)
- **Instance names**: Follow camelCase matching class names (`staffTable`, `testTypeTableManager`)

**Rationale**:
- Clear distinction between read-only and management functionality
- Consistent naming pattern for future table implementations
- Eliminates confusion about table capabilities
- Supports scalable architecture for government applications

**File Naming**:
- **Read-only tables** → `[entity]-table.js` (e.g., `staff-table.js`, `test-type-table.js`)
- **Management tables** → `[entity]-table-manager.js` (e.g., `test-type-table-manager.js`)

**Class Naming**:
- **Read-only tables** → `[Entity]Table` class, `[entity]Table` instance
- **Management tables** → `[Entity]TableManager` class, `[entity]TableManager` instance

**Decision Matrix for Naming**:
- **Read-only functionality** → `[entity]-table.js` (e.g., `staff-table.js`, `test-type-table.js`)
- **Management functionality** → `[entity]-table-manager.js` (e.g., `test-type-table-manager.js`)

#### Implementation Standards & Best Practices

**Required Standards**:
1. **Use schema-driven approach**: All tables must use `tableSchema` configuration
2. **Extend from BaseTable hierarchy**: Choose appropriate base class
3. **Include required methods**: `fetchData()`, `handleError()` (legacy methods only for special cases)
4. **Follow naming conventions**: Use standardized file and class naming patterns
5. **Leverage configuration**: Store table schema and entity-specific terms in config files
6. **Use MESSAGE_CONFIG_MAPS**: Required for all message handling

**Security Requirements**:
6. **Always use `escapeHtml()`**: Required for all user-generated content (even internal apps)
7. **Proper error handling**: Use `showMessage()` for consistent GC-compliant error display
8. **Async pattern**: Use async/await with Promise wrapper for API calls

**Standard Implementation Flow**:
- `create()` → Loading state → `fetchData()` → `buildTableFromSchema()` → WET-BOEW init → `postInitialize()` → Error handling

**Code Quality Requirements**:
- **English comments only**: All code comments must be in English
- **jQuery DOM operations**: Use jQuery for all DOM manipulation (GCWeb compatibility)
- **Consistent imports**: Follow standardized import order and structure
- **Error handling**: Include try/catch blocks in `fetchData()` methods
- **Empty state handling**: Use BaseTable's `showEmptyState()` method

#### Architecture Decision Process

**For New Entity Tables**:

1. **Assess Requirements**:
   - Does the entity need editing/management functionality?
   - Will users need to perform bulk operations?
   - Is it display-only or interactive?

2. **Choose Base Class**:
   - **Display only** → Use `ReadOnlyTable`
   - **Bulk operations needed** → Use `BulkCheckboxTable`

3. **Determine Naming**:
   - **Read-only table** → `[entity]-table.js`
   - **Management table** → `[entity]-table-manager.js`

4. **Implementation Checklist**:
   - [ ] Define `tableSchema` in entity configuration
   - [ ] Extend appropriate base class (`ReadOnlyTable` or `BulkCheckboxTable`)
   - [ ] Implement required methods: `fetchData()`, `handleError()`
   - [ ] Add `MESSAGE_CONFIG_MAPS` configuration
   - [ ] Add entity-specific formatters if needed
   - [ ] Add proper error handling in `fetchData()`
   - [ ] Follow import order standards
   - [ ] Write English comments only

**Example Decision Tree**:
```
New Entity Table Needed
├── Display Only?
│   ├── Yes → ReadOnlyTable → [entity]-table.js
│   └── No → Continue
├── Management Needed?
│   ├── Yes → Need both types
│   │   ├── ReadOnlyTable → [entity]-readonly-table.js
│   │   └── BulkCheckboxTable → [entity]-manager.js
│   └── No → ReadOnlyTable → [entity]-table.js
```