/*
 * Base Table Class
 * Foundation for all table implementations in Sed-LIMS
 * Provides standardized WET-BOEW DataTables integration and GC Web Standards compliance
 */
import { showMessage } from '../helpers/message-helpers.js';
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { buildTableMessageConfig, buildTableHtml, initializeWETTable } from '../helpers/table-helpers.js';
import { escapeHtml, formatDate, formatNotAvailable, getRoleDisplayName } from '../helpers/format-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

export class BaseTable {
    constructor(config) {
        this.containerId = config.containerId;
        this.tableId = config.tableId;
        this.entityConfig = config.entityConfig;
        this.messageConfigMaps = config.messageConfigMaps;
    }

    // Standard table creation flow with simple configuration override
    async create(config = {}) {
        // Simple configuration override - update properties if provided
        if (config.containerId) this.containerId = config.containerId;
        if (config.tableId) this.tableId = config.tableId;
        if (config.userInfo) this.userInfo = config.userInfo;

        const $container = $(`#${this.containerId}`);

        if (!$container.length) {
            console.warn(`Table container not found: ${this.containerId}`);
            throw new Error('Container not found');
        }

        try {
            // Show loading state
            this.showLoadingState($container);

            // Fetch data (implemented by subclasses)
            const data = await this.fetchData();

            // Handle empty data
            if (!data || data.length === 0) {
                this.showEmptyState($container);
                return;
            }

            // Build and display table
            const tableHtml = this.buildTable(data);
            $container.html(tableHtml);

            // Initialize WET-BOEW DataTables
            await initializeWETTable(this.tableId);

            // Post-initialization setup (optional, implemented by subclasses)
            this.postInitialize();

        } catch (error) {
            this.handleError($container, error);
            throw error;
        }
    }

    // Show loading state with accessibility
    showLoadingState($container) {
        const loadingText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.common.loading }, 'message');
        $container.html(`<p role="status" aria-live="polite">${loadingText}...</p>`);
    }

    // Show empty state with localized message
    showEmptyState($container) {
        const entityTerm = getLocalizedText({ message: this.entityConfig.entityTerms }, 'message');
        const emptyTableTemplate = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable }, 'message');
        const emptyTableText = emptyTableTemplate.replace('{entity}', entityTerm);
        $container.html(`<div class="alert alert-info"><p>${emptyTableText}</p></div>`);
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', this.messageConfigMaps);
    }

    // Build standard WET-BOEW table HTML using schema-driven approach
    buildTable(data) {
        const messageConfig = buildTableMessageConfig(this.entityConfig.entityTerms);

        // All tables must use schema-driven approach
        if (!this.entityConfig.tableSchema || !this.entityConfig.tableSchema.columns) {
            throw new Error(`Table "${this.tableId}" must define tableSchema in entity configuration`);
        }

        return this.buildTableFromSchema(data, messageConfig);
    }

    // Schema-driven table building (new approach)
    buildTableFromSchema(data, messageConfig) {
        let schema = this.entityConfig.tableSchema;

        // Handle management table schema extension
        if (this.entityConfig.managementTableSchema && this instanceof BulkCheckboxTable) {
            schema = this.extendSchemaForManagement(schema, this.entityConfig.managementTableSchema);
        }

        // Filter columns based on conditions
        const visibleColumns = schema.columns.filter(column => {
            if (column.conditional && column.condition) {
                return column.condition(this.currentConfig || {});
            }
            return true;
        });

        // Build headers from visible columns
        const headerCells = visibleColumns.map(column => {
            if (!column.header) {
                throw new Error(`Column "${column.key}" must define header property`);
            }
            const headerText = getLocalizedText({ message: column.header }, 'message');
            return `<th scope="col">${headerText}</th>`;
        }).join('');

        // Build rows from schema using visible columns
        const tableRows = data.map(row => {
            const cells = visibleColumns.map(column => {
                // Get raw value using accessor
                const rawValue = column.accessor ? column.accessor(row) : row[column.key];

                // Apply formatter
                let cellValue;
                if (column.cell) {
                    // Custom cell function
                    cellValue = column.cell(rawValue, row);
                } else if (column.formatter) {
                    // Formatter system
                    cellValue = this.applyFormatter(column.formatter, rawValue, row);
                } else {
                    // Default: escape HTML
                    cellValue = escapeHtml(rawValue || '');
                }

                return `<td>${cellValue}</td>`;
            }).join('');

            return `<tr>${cells}</tr>`;
        }).join('');

        // Get table options from schema or use defaults
        const tableOptions = this.getTableOptionsFromSchema(schema, visibleColumns);

        return buildTableHtml({
            tableId: this.tableId,
            headerCells,
            tableRows,
            messageConfig,
            tableOptions
        });
    }



    // Extend base schema with management table additions
    extendSchemaForManagement(baseSchema, managementSchema) {
        const extendedColumns = [...baseSchema.columns];

        // Replace the last column (status) with checkbox column if defined
        if (managementSchema.checkboxColumn) {
            extendedColumns[extendedColumns.length - 1] = managementSchema.checkboxColumn;
        }

        return {
            columns: extendedColumns,
            defaultOptions: {
                ...baseSchema.defaultOptions,
                ...managementSchema.defaultOptions
            }
        };
    }

    // Get table options from schema configuration
    getTableOptionsFromSchema(schema, visibleColumns = null) {
        const columnsToUse = visibleColumns || schema.columns;
        const options = {};

        // Set up column definitions for sorting and other DataTables features
        if (columnsToUse) {
            options.columnDefs = [];

            columnsToUse.forEach((column, index) => {
                const columnDef = { targets: [index] };

                // Handle sorting configuration
                if (column.sort === false || column.orderable === false) {
                    columnDef.orderable = false;
                }

                // Handle custom sorting data
                if (column.dataOrder) {
                    // This would require more complex implementation for custom sorting
                    // For now, we'll use the standard DataTables sorting
                }

                // Handle column visibility
                if (column.visible === false) {
                    columnDef.visible = false;
                }

                // Handle column width
                if (column.width) {
                    columnDef.width = column.width;
                }

                // Only add columnDef if it has properties
                if (Object.keys(columnDef).length > 1) {
                    options.columnDefs.push(columnDef);
                }
            });
        }

        // Merge with any custom table options from subclass
        const customOptions = this.getTableOptions();
        return { ...options, ...customOptions };
    }

    // Abstract method to be implemented by subclasses
    async fetchData() {
        throw new Error('fetchData() must be implemented by subclass');
    }

    // Optional methods that can be overridden
    getTableOptions() {
        return {}; // Default empty options
    }

    postInitialize() {
        // Optional post-initialization logic
    }

    // Apply formatter to value
    applyFormatter(formatterName, value, row) {
        if (BaseTable.formatters[formatterName]) {
            return BaseTable.formatters[formatterName](value, row);
        }

        // Check for entity-specific formatters
        if (this.entityFormatters && this.entityFormatters[formatterName]) {
            return this.entityFormatters[formatterName](value, row);
        }

        console.warn(`Formatter not found: ${formatterName}`);
        return escapeHtml(value || '');
    }

    // Static helper methods for common cell formatters (used in tableSchema)
    static formatters = {
        // Safe HTML escaping with "Not Available" fallback (most common use case)
        safeText: (value) => escapeHtml(value || formatNotAvailable()),

        // Date formatting with fallback
        formatDate: (value) => value ? formatDate(value) : formatNotAvailable(),

        // Date with updated_at/created_at fallback
        formatDateWithFallback: (value, row) => {
            const dateToUse = value || row.updated_at || row.created_at;
            return dateToUse ? formatDate(dateToUse) : formatNotAvailable();
        },

        // Role display formatter
        roleDisplay: (value) => getRoleDisplayName(value)
    };
}

// Specialized base class for read-only tables
export class ReadOnlyTable extends BaseTable {
    constructor(config) {
        super(config);
    }

    // Read-only tables typically don't need post-initialization
    postInitialize() {
        // No additional setup needed for read-only tables
    }
}

// Specialized base class for tables with bulk checkbox operations
export class BulkCheckboxTable extends BaseTable {
    constructor(config) {
        super(config);
        this.hasChanges = false;
    }

    // Override schema-driven table building to add bulk actions
    buildTableFromSchema(data, messageConfig) {
        const tableHtml = super.buildTableFromSchema(data, messageConfig);
        const bulkActionsHtml = this.buildBulkActionsHtml();
        return tableHtml + bulkActionsHtml;
    }

    // Build bulk actions HTML (to be implemented by subclasses)
    buildBulkActionsHtml() {
        // Default implementation - subclasses should override this
        return '';
    }

    // Bulk checkbox tables need event handlers setup
    postInitialize() {
        this.initializeEventHandlers();
    }

    // Abstract method for event handlers
    initializeEventHandlers() {
        // To be implemented by subclasses that need event handling
    }

    // Common change tracking functionality
    trackChanges(hasChanges) {
        this.hasChanges = hasChanges;
        this.toggleBulkActions();
    }

    toggleBulkActions() {
        // Default implementation - subclasses should override this
        // This method should enable/disable buttons based on this.hasChanges
        console.warn('toggleBulkActions() should be overridden by subclass');
    }
}