/*
 * Staff Table
 * Read-only table for displaying laboratory staff information
 * Uses ReadOnlyTable for standardized WET-BOEW DataTables implementation
 * Follows GC Web Standards and eliminates code duplication
 */
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { LabApi } from '../../core/services/lab-api.js';
import { STAFF_CONFIGS } from '../../core/config/staff-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    staff: STAFF_CONFIGS
};

class StaffTable extends ReadOnlyTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || STAFF_CONFIGS.ui.staffTable.containerId,
            tableId: options.tableId || STAFF_CONFIGS.ui.staffTable.tableId,
            entityConfig: {
                ...STAFF_CONFIGS.ui.staffTable,
                entityTerms: STAFF_CONFIGS.ui.staffTable.entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });
    }

    // Fetch staff data from API with error handling
    async fetchData() {
        try {
            const staffData = await new Promise((resolve, reject) => {
                LabApi.getLabStaff()
                    .done(resolve)
                    .fail(reject);
            });
            return staffData || [];
        } catch (error) {
            console.error('Failed to fetch staff data:', error);
            throw error;
        }
    }

    // Handle errors with standard GC error display
    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create staff table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Factory function to create staff table instances
export function createStaffTable(options = {}) {
    return new StaffTable(options);
}

// Create and export singleton instance for default usage
export const staffTable = new StaffTable();
