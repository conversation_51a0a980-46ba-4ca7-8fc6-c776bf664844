# Sed-LIMS Frontend Development Guide

> **Purpose**: AI Assistant reference for Sed-LIMS frontend development

## 🎯 ESSENTIAL CODING STANDARDS

### Core Rules
- **jQuery DOM operations**: Use jQuery for all DOM manipulation (GCWeb compatibility)
- **ES6 modules + jQuery**: Modern imports with jQuery for DOM operations
- **GC Standards**: Use only official gcweb/wet-boew components
- **Config-driven**: Use entity-specific configs for all implementations
- **English comments**: All code comments must be in English
- **Bilingual support**: EN/FR support mandatory

### Import Order Standard
1. Base classes (table-base.js)
2. Helper functions (message-helpers.js, format-helpers.js)
3. API services (*-api.js)
4. Configuration files (*-configs.js)

### Error Handling Pattern
```javascript
async fetchData() {
    try {
        const data = await new Promise((resolve, reject) => {
            ApiService.getData()
                .done(resolve)
                .fail(reject);
        });
        return data || [];
    } catch (error) {
        console.error('Failed to fetch data:', error);
        throw error;
    }
}
```

## 🔧 CONFIGURATION SYSTEM

### Standard Config Structure
```javascript
export const ENTITY_CONFIGS = {
    functional_settings: {
        // Entity-specific business logic settings
    },
    ui: {
        entityTable: {
            containerId: 'entity-table-container',
            tableId: 'entity-table',
            entityTerms: { en: 'entities', fr: 'entités' },
            tableSchema: {
                columns: [
                    { key: 'name', header: { en: 'Name', fr: 'Nom' } },
                    { key: 'status', header: { en: 'Status', fr: 'Statut' }, formatter: 'entityStatus' }
                ],
                defaultOptions: {
                    order: [[1, "desc"]]
                }
            }
        }
    },
    messages: {
        // Use global message templates - keep this section minimal
    }
};
```

### Message System
- Use `showMessage()` with config paths
- Use `MESSAGE_CONFIG_MAPS` for config resolution
- Use global message templates when possible

## 📊 TABLE IMPLEMENTATION

### Base Classes
- `ReadOnlyTable` - For display-only tables
- `BulkCheckboxTable` - For tables with bulk operations

### Standard Table Template
```javascript
import { ReadOnlyTable } from '../../core/components/table-base.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { EntityApi } from '../../core/services/entity-api.js';
import { ENTITY_CONFIGS } from '../../core/config/entity-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    entity: ENTITY_CONFIGS
};

class EntityTable extends ReadOnlyTable {
    constructor(options = {}) {
        super({
            containerId: options.containerId || ENTITY_CONFIGS.ui.entityTable.containerId,
            tableId: options.tableId || ENTITY_CONFIGS.ui.entityTable.tableId,
            entityConfig: {
                ...ENTITY_CONFIGS.ui.entityTable,
                entityTerms: ENTITY_CONFIGS.ui.entityTable.entityTerms
            },
            messageConfigMaps: MESSAGE_CONFIG_MAPS
        });
    }

    async fetchData() {
        try {
            const data = await new Promise((resolve, reject) => {
                EntityApi.getData()
                    .done(resolve)
                    .fail(reject);
            });
            return data || [];
        } catch (error) {
            console.error('Failed to fetch entity data:', error);
            throw error;
        }
    }

    handleError($container, error) {
        $container.empty();
        console.error(`Failed to create entity table "${this.tableId}":`, error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

export { EntityTable };
export const entityTable = new EntityTable();
```

## 🎨 GC WEB STANDARDS

### Form Components
- Use `input-lg` for larger inputs
- Use `gc-chckbxrdio` for checkboxes/radios
- Use `btn btn-primary` for primary actions
- Use `btn btn-default` for secondary actions

### Required Patterns
- All forms must have bilingual labels
- All inputs must have proper WCAG accessibility
- Use WET-BOEW components for complex functionality

## 💬 MESSAGING PATTERNS

### Message Types
- **DANGER**: Persistent errors (user must dismiss)
- **WARNING**: Auto-dismiss warnings (3s)
- **SUCCESS**: Auto-dismiss success (3s)
- **INFO**: Auto-dismiss information (3s)

### Usage
```javascript
// Success message
showMessage('#page-success-container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});

// Error message
showMessage('#page-error-container', 'global.messages.errors.validation', 'required', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm
});
```

## 🔍 TROUBLESHOOTING

### Common Issues
- **Table not initializing**: Check WET-BOEW initialization timing
- **Config not found**: Verify import paths and config structure
- **Message not displaying**: Check MESSAGE_CONFIG_MAPS setup
- **GC component not working**: Verify gcweb/wet-boew class usage

### Debugging
- Use browser dev tools for DOM inspection
- Check console for JavaScript errors
- Verify API responses in Network tab
- Test bilingual functionality

## 📝 IMPLEMENTATION CHECKLIST

### New Table Implementation
1. ✅ Create entity-specific config file
2. ✅ Extend appropriate base class (ReadOnlyTable/BulkCheckboxTable)
3. ✅ Define tableSchema in config
4. ✅ Implement fetchData() method
5. ✅ Set up MESSAGE_CONFIG_MAPS
6. ✅ Test bilingual functionality
7. ✅ Verify GC Web Standards compliance

### Code Quality Standards
- ✅ Use jQuery for all DOM operations
- ✅ Follow import order standard
- ✅ Use async/await + try/catch for error handling
- ✅ Use config-driven approach
- ✅ Write English comments
- ✅ Test bilingual support
- ✅ Verify WCAG compliance
